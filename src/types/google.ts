import { IAudienceDetail } from '@/types/audience';

export interface IGoogleUser {
  ad_account_default: null;
  ad_accounts: string[];
  avatar_url: string;
  core_user_id: string;
  name: string;
  scope: number[];
  user_id: number;
}

export interface IAdsAccount {
  ad_account_id: string;
  ad_account_name: string;
}

export interface ICustomAudienceResponse {
  items: IAudienceDetail[];
  count: number;
}

export interface IGoogleHistoryItem {
  job_id: number;
  user_id: number;
  segment_id: 0;
  status: 'COMPLETED' | 'FAILED' | 'PENDING' | 'PROCESSING';
  previous_total_records: number;
  workflow_id: string;
  airflow_dag_run_id: string;
  id: number;
  records: number;
  error_message: string;
  date_completed: string;
  retry_count: number;
  is_initial: boolean;
  segment_info: {
    name: string;
    color: string;
  };
}

export interface IGoogleHistoryResponse {
  items: IGoogleHistoryItem[];
  count: number;
}
