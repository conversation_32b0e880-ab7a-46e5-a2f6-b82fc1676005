import { camelize } from '@/lib/utils';
import { contactHistory } from '@/locale/en/contactHistory';
import { verify } from '@/locale/en/verify';
import { footer } from '@/locale/en/footer';
import { reportContact } from '@/locale/en/reportContact';
import { howtoConnect } from '@/locale/en/howtoConnect';
import { getPhoneNumber } from '@/locale/en/getPhoneNumber';
import { limitAlert } from '@/locale/en/limitAlert';
import { email } from './email';
import { segmentLog } from '@/locale/en/segmentLog';
import { tiktokAds } from '@/locale/en/tiktokAds';
import { audience } from '@/locale/en/audience';
import { googleAds } from '@/locale/en/googleAds';

export default camelize({
  common: {
    success: 'Success',
    warning: 'Warning',
    inprogress: 'In progress',
    connect: {
      title: 'Connect to {{value}} Account',
      subTitle:
        'When you connect a {{value}} account to CRM360, you can publish to it, create Custom Audience, create reports, and more.',
    },
    days: 'Days',
    minutes: 'Minutes',
    expired: 'Expired',
    contacts: 'contacts',
    pending: 'Pending',
    failed: 'Failed',
    reload: 'Reload',
    update: 'Update',
    noMoreData: 'No more data',
    updateHistory: 'Update History',
    customAudienceName: 'Custom Audience Name',
    totalSegments: 'Total Segments',
    totalContacts: 'Total Contacts',
    disconnect: 'Disconnect',
    lastUpdate: 'Last Update',
    addCustomAudience: 'Create Custom Audience',
    select: {
      title: 'Select to {{value}} Account',
      subTitle:
        'When you select a {{value}} account to CRM360, you can publish to it, create Custom Audience, create reports, and more.',
    },
    zaloAds: {
      tempName: 'ZNS Template',
      updateCampaign: 'Update Campaign',
      status: 'Status',
      actionBtn: {
        businessWebsite: 'Business website',
        call: 'Call',
        oaInfo: 'OA information',
        zaloMiniApp: 'Zalo Mini App page',
        productDistribution: 'Product distribution page',
        otherWeb: 'Other web pages',
        applicationPage: 'Application page',
        otherApplicationPage: 'Other application page',
        connectToZalo: 'Connect to Zalo Account',
      },
      zaloDescription:
        'When you connect a Facebook account to CRM360, you can publish to it, create Push Custom Audience, create reports, and more.',
      previewZns: 'Preview ZNS',
      znsTemplateName: 'ZNS Template Name',
      addLogoOrImage: 'Add logo or image',
      title: 'Zalo',
      subTitle: 'Create and manage Custom Audience for Zalo from CRM360 contact list Audiences.',
      button: 'Button',
      zaloOa: 'Zalo OA',
      zaloZns: 'Zalo ZNS',
      templateZns: 'Template ZNS',
      sendZns: 'Send ZNS',
      campaign: 'Campaign',
      createNewTemplate: 'Create new template',
      searchTemplate: 'Search Template',
      noData: 'No data available',
      subNoData: 'It looks like there’s no data available yet. Try adding some new items.',
      createContent: 'Create Content',
      zaloAccount: 'Zalo Account',
      type: 'Type',
      label: 'Label',
      mainButton: 'Main Button',
      subButton: 'Sub Button',
      addRow: 'Add Row',
      addButton: 'Add Button',
      actionButton: 'Action Button',
      roleActionButton: 'You can only add 2 buttons',
      thanksForUse: 'Thank you, Mr./Ms. <customerName> for shopping with us.',
      confirmYourOrder: 'Your order has been confirmed with the following details:',
      titleKey: 'Title',
      contentKey: 'Content',
      tableKey: 'Table',
      goToBusinessPage: "Go to Business's page",
      next: 'Next',
      back: 'Back',
      parameter: 'Parameter',
      paraName: 'Para Name',
      paraType: 'Para Type',
      paraValue: 'Para Value',
      note: 'Note',
      enterNote: 'Enter note...',
      term: 'I have read and agree to the ',
      darkMode: 'Dark Mode',
      darkTheme: 'Dark Theme',
      lightTheme: 'Light Theme',
      selectType: 'Select Type',
      addKey: 'Add Key',
      errorKey: 'Some key not exist, you want to continue',
      confirmParamType: 'Confirm Param Type',
      confirmParamTypeDesc:
        "The parameters you entered do not match the system's data. Do you want to continue?",
      sendForAppoval: 'Send for appoval',
      description: 'Description',
      imageRequired: 'Image is required',
      send: 'Send',
      acceptTerm: 'Please accept the',
      complete: 'Complete',
      upload:
        'To upload an image or logo, please make sure it meets the requirements: a height of 96 pixels and a file size of less than 25MB',
      znsId: 'ZNS ID',
      deleteTemplate: 'Delete Template',
      subDeleteTemplate:
        'You are about to delete this ZNS Template. This action cannot be undone. Are you sure you want to proceed?',
      createCampaign: 'Create Campaign',
      enterCampaign: 'Enter Campaign Name',
      template: 'Template',
      chooseTemplate: 'Choose Template',
      segment: 'Segment',
      chooseSegment: 'Choose Segment',
      totalContact: 'Total Contact',
      expectedAmount: 'Expected Amount',
      scheduleCampaign: 'Schedule campaign',
      termAndCondition: 'Term and Condition',
      dateCreated: 'Date Created',
      sendTime: 'Send Time',
      success: 'Success',
      failure: 'Failure',
      appreciate: 'We appreciate your trust in our service!',
      sendCampaign: 'Send Campaign Immediatly',
      subSendCampaign:
        'Are you sure you want to ignore the schedule and send this campaign immediatly.',
      deleteCamp: 'Delete Campaign',
      subDeleteCamp:
        'You are about to delete this Campaign. This action cannot be undone. Are you sure you want to proceed?',
      appreciateService: 'We appreciate your trust in our service!',
      scheduleNotice:
        'This campaign will be sent immediately after creation unless you set a schedule.',
    },
    error: {
      title: 'Error',
      selectDate: '*Please select date',
      selectTime: '*Please select time',
      overDue: '*Select a date and time at least 10 minutes in the future',
      enterYourName: 'Please enter your name',
      enterYourNumber: 'Please enter your number',
      enterYourSegmentName: 'Please enter your segment name',
      errorAudio: 'Error loading audio',
      noResponse: 'No response',
      noItemFound: 'No item found',
    },
    facebookAds: {
      goToFbAds: 'Go to Facebook Ads',
      date: 'Date',
      accountBusinessNotice: 'This account must be owned by a business to be selected.',
      actionRequired: 'Action Required:',
      descriptionActionRequired:
        "You haven't accepted Facebook's Terms and Policies.Please review and accept them to proceed.",
      ownedBy: 'Owned by:',
      inactive: 'Inactive',
      active: 'Active',
      acceptTerm: "Accept Facebook's Term",
      switchAccount: 'Switch Account',
      searchCampaign: 'Search custom campaign',
      totalContact: 'Total Contact',
      enterAudienceName: 'Enter Audience Name',
      confirmLogout: 'Are you sure you want to log out from your facebook?',
      subConfirmLogout: 'You can’t access any data after this action.',
      logoutFromFb: 'Log Out from Facebook',
      title: 'Facebook Ads',
      subTitle:
        'Create and manage Push Custom Audience for Facebook Ads from CRM360 contact list Audiences.',
      connectFacebook: 'Connect to Facebook Account',
      subConnectFacebook:
        'When you connect a Facebook account to CRM360, you can publish to it, create Push Custom',
      moreConnect: 'Audience, create reports, and more.',
      addFacebookAccount: 'Add Facebook Account',
      pushDate: 'Push Date',
      importDate: 'Import Date',
      searchTemplate: 'Search custom audience',
      pushCustomAudience: 'Push Custom Audience',
      customAudience: 'Custom Audience',
      login: {
        fail: 'Facebook log in failure. Please try again.',
        success: 'Login successfully',
      },
      adsAccount: 'Ads Account',
      audiences: {
        segmentPlaceholder: 'Select segment from dropdown list or enter Custom Audience name',
        notice: 'The Custom Audience name defaults to the Segment name if not entered.',
        segment: 'Segment',
        name: 'Custome Audience Name',
        title: 'Custom Audiences',
        addCustomAudience: 'Create Custom Audience',
        chooseSegment:
          ' Choose a Segment to create a Custom Audience for your Facebook Ads account',
        pushToFb: 'Push to Facebook',
      },
      account: {
        title: 'Select Facebook Account',
        description:
          'Choose the Facebook Pages, groups and profile that you would like to set default to CRM360.',
        subTitle: 'You need to be a Admin or Editor for any page you want to add to CRM360.',
        portfolios: 'Business portfolio{{value}}',
        businessAsset: 'Business asset',
        selectYourBusiness: 'Select your business portfolios',
        tooltip:
          'Business portfolios let you manage your business assets together and securely assign other people access to them.',
      },
      campaign: {
        title: 'Campaign',
        distribution: 'Distribution',
        amountSpent: 'Amount Spent',
        endDate: 'End Date',
        impressions: 'Impressions',
        budget: 'Budget',
        enterBudget: 'Enter Budget',
        approacher: 'Approacher',
        budgetUnderLimit: 'Budget must be greater than 100',
      },
    },
    reminder: {
      title: 'Reminder',
      createSuccess: 'Create reminder successfully',
      createFailed: 'Failed to create reminder',
      overDue: 'Your reminder is overdue. Catch up',
      errorDate: 'Date must be greater than the current date',
      errorDateTenMinutes: 'Date must be greater than the current date by 10 minutes',
      deleteTitle: 'Delete Reminder',
      confirmDelete: 'Are you sure you want to delete reminder?',
      subTitle: 'This action cannot be undone.',
      delete: 'Delete reminder successfully',
      update: 'Update reminder successfully',
      tenMinutes:
        'You have an appointment with the customer {{contactName}}. Please prepare the necessary documents.',
      overDueReminder: 'You missed your appointment with {{count}} contact{{symbol}}.',
      viewDetail: 'View Detail',
    },
    button: {
      close:'Close',
      upload: 'Upload',
      schedule: 'Schedule',
      noOption: 'No option',
      default: 'Default',
      setDefault: 'Set Default',
      selected: 'Selected',
      reNew: 'Re-new',
      cancelRenew: 'Cancel Renewal',
      logout: 'Log out',
      disconnect: 'Disconnect',
      lightMode: 'Light Mode',
      darkMode: 'Dark Mode',
      export: 'Export',
      backToHome: 'Back to Home',
      segment: 'Segment',
      preview: 'Preview',
      addContact: 'Add Contact',
      add: 'Add',
      getPhoneNumber: 'Get Phone Number',
      apply: 'Apply',
      clearAll: 'Clear All',
      addSegment: 'Add Segment',
      cleanUp: 'Clean Up',
      viewDetails: 'View Details',
      setSchedule: 'Set Schedule',
      edit: 'Edit',
      ok: 'Ok',
      deleteContact: 'Delete Contact',
      moveToTrash: 'Move to trash',
      cancel: 'Cancel',
      refetch: 'Refetch',
      reload: 'Reload',
      remove: 'Remove',
      report: 'Report',
      view_more: 'View More',
      view_more_phone: 'View More Number',
      saveChange: 'Save change',
      save: 'Save',
      select: 'Select',
      restore: 'Restore',
      emptyTrashNow: 'Empty Trash now',
      undo: 'Undo',
      current: 'Current',
      backToContactList: 'Back to contact list',
      goBack: 'Go back',
      time: 'Time',
      date: 'Date',
      overDue: 'Overdue',
      create: 'Create',
      decline: 'Decline',
      delete: 'Delete',
      addCustomAudience: 'Add Custom Audience',
      push: 'Push',
      done: 'Done',
      topUp: 'Top Up',
      logOut: 'Log out',
      connected: 'Connected',
      retry: 'Retry',
      viewAllSegment: 'View all segment',
      accept: 'Accept',
      showMore: 'Show more',
      onOff: 'On/Off',
      verify: 'Verify',
      hour: 'Hour',
      minute: 'Minute',
      min: 'Min',
      note: 'Note',
      more: 'More',
      province: 'Province',
      company: 'Company',
      seconds: 'Seconds',
      duration: 'Duration',
      pic: 'PIC',
      demographic: 'Demographic',
      age: 'Age',
      gender: 'Gender',
      dob: 'DOB',
      city: 'City',
      status: 'Status',
      sort: 'Sort',
      contactFrequency: 'Call Count',
      verifyNumber: 'Verify Number',
      month: 'Month',
      day: 'Day',
      year: 'Year',
      name: 'Name',
      phoneNumber: 'Phone Number',
      email: 'Email',
      address: 'Address',
      ward: 'Ward',
      district: 'District',
      position: 'Position',
      price: 'Price',
      filterQuantity: 'Filter Quantity',
      contactHistory: 'Contact History',
      cityOrProvince: 'City/Province',
      available: 'Available',
      reported: 'Reported',
      fullName: 'Full Name',
      confirmed: 'Confirmed',
      unconfirm: 'Unconfirm',
      spam: 'Spam',
      rowPage: 'Rows per page',
      invalid: 'Invalid',
      valid: 'Valid',
      unverify: 'Unverify',
      solid: 'Solid',
      gradient: 'Gradient',
      not_exist_bought_phone: 'Not exist bought phone or reported',
      pls_click_btn_select: 'Please click button select to view contact',
      back_to_bought_phones: 'Back to bought phones',
      setReminder: 'Set Reminder',
      history: 'History',
      content: 'Content',
      missedCall: 'Missed Call',
      completedCall: 'Completed Call',
      facebookBroadcast: 'Facebook Broadcast',
      marketingEmail: 'Marketing Email',
      zaloBroadcast: 'Zalo Broadcast',
      all: 'All',
      call: 'Call',
      zalo: 'Zalo',
      facebook: 'Facebook',
      tagColor: 'Tag’s color',
      calling: 'Calling',
      recording: 'Recording',
      callScript: 'Call Script',
      failToConnectHotline: "Can't connect Switchboard",
      noOptions: 'No options',
      noScript: 'No script to show',
      cleaningUp: 'Cleaning Up',
      getSegment: 'Get Segment',
      confirm: 'Confirm',
      unread: 'Unread',
      read: 'Read',
      createNew: 'Create New',
      connect: 'Connect',
      selectFacebookAccount: 'Select Facebook Account',
      selectTiktokAccount: 'Select TikTok Ads Account',
      selectGoogleAccount: 'Select Google Ads Account',
      clearFilter: 'Clear Filter',
      dismiss: 'Dismiss',
      reminderOverdue: 'Reminder Overdue',
      continue: 'Continue',
      send: 'Send',
      buyMore: 'Buy more',
      createNewSegment: 'Create New Segment',
    },
    validate: {
      required: 'This field is required',
      emailInvalid: 'Invalid email format',
      phoneNumberInvalid: 'Invalid phone number format',
      userNameMinLength: 'Username must be at least {{value}} characters',
      userNameMaxLength: 'Username must be at most {{value}} characters',
      phoneNumberMinLength: 'Phone number must be at least {{value}} characters',
      phoneNumberMaxLength: 'Phone number must be at most {{value}} characters',
      segmentMinLength: 'Segment name must be at least {{value}} characters',
      validateError: 'Please check your file again',
      notEmpty: '! Not empty',
      phoneNumberName: 'Phone number and name is required',
    },
    selectOrCreateSegment: 'Select or create Segment',
    activity: 'Activity',
    takeNote: 'Take a note',
    enterSomeThing: 'Enter something',
    chooseYourSegmentOrTypeToCreateNew: 'Choose your segment or type to create new',
    emptySegment: "You don't have any segment",
    outgoingNumber: 'Outgoing Number',
    rentPhoneNumber: 'Rent phone number',
    getHotline: 'Get hotline',
    searchPlaceHolder: 'Search by name, phone number, email,...',
    searchByName: 'Search by name',
    resultsFound: 'Result{{symbol}} found',
    noResults: 'noResults',
    noData: 'No data to display',
    noDataHistoryDescription:
      'When you interact with the features, the information will be recorded in the contact history',
    noNoti: 'No Notifications',
    noDataDescriptions: 'It looks like there’s no data available yet. Try adding some new items.',
    noNotiDescriptions: 'You have no notifications right now.',
    filterOptionToCreateSegment: 'Filter options to create segment',
    search: 'Search ...',
    male: 'Male',
    female: 'Female',
    other: 'Other',
    january: 'January',
    february: 'February',
    march: 'March',
    april: 'April',
    may: 'May',
    june: 'June',
    july: 'July',
    august: 'August',
    september: 'September',
    october: 'October',
    november: 'November',
    december: 'December',
    location: 'Location',
    type: 'Type',
    facebookCall: 'Facebook Call',
    zaloCall: 'Zalo Call',
    empty: 'Empty',
    contactDetail: {
      personalInformation: 'Personal Information',
      contactInformation: 'Contact Information',
    },

    segment: {
      title: 'Segment',
      titleMulti: 'Segments',
      createSegment: 'Create Segment',
      updateSegment: 'Update Segment',
      callScript: 'Call Script',
      enterSegmentName: 'Enter Segment’s Name',
      description: 'Description',
      segmentName: 'Segment’s Name',
      totalContact: 'Total Contact',
      createDate: 'Create Date',
      descriptionPage: 'Easily create and manage campaigns synced with your CRM.',
      titleDeleteSegment: 'Delete Segment',
      deleteSegmentDescription:
        'You are about to delete this segment. This action cannot be undone. Are you sure you want to proceed?',
      segmentMovedToTrash: 'Segment moved to trash',
      segmentCreateSuccess: 'Created segment successfully',
      segmentUpdateSuccess: 'Updated segment successfully',
      addMoreContact: 'Add more contact',
      totalContacts: 'Total Contacts',
    },
    contactList: {
      selectYourSegment: 'Select your segment or type to create new',
      descriptionPage: 'Easily create and manage campaigns synced with your CRM.',
      titlePage: 'Contact List Audiences',
      titlePageBreadcrumb: 'Contact List',
      titleHistory: 'Contact History',
      uploadContact: 'Upload Contact',
      addNewContact: 'Add new contact',
      maximumFile: 'Maximum file size is 25MB',
      dropFile: 'Drop your file here or',
      browse: 'browse',
      fromComputer: 'from your computer',
      upload: 'Upload',
      downloadTemplate: 'Download Template',
      selectDataOptions: 'Select Data Option to fill your template.',
      dataOption: 'Data Option',
      selectedOption: 'Selected Option',
      titleAddContact: 'Add Contact',
      addFormBig360: 'Add from Big360',
      titleEditContact: 'Edit Contact',
      uploadSuccess: 'Upload successfully',
      report_phone: 'Report phone number',
      confirm_report: 'Are you sure you want to report this phone number?',
      not_undone: 'This action cannot be undone.',
      titleDeleteContact: 'Remove Contact',
      deleteContactDescription: 'Are you sure you want to move this contact to trash?',
      contactMovedToTrash: 'Contact moved to trash',
      trashTitlePage: 'Trash',
      archivedTitlePage: 'Archived',
      trashDescriptionPage:
        'Contacts that have been in Trash more than 30 days will be deleted permanently',
      contactsRestored: 'Contacts restored',
      yourChangeHasBeenSavedSuccessfully: 'Your change has been saved successfully',
      emptyTrash: 'Empty Trash',
      emptyTrashDescription: 'Are you sure you want to empty trash?',
      fileFormatWasWrong: 'File format was wrong. You can check file download to fix that!!!',
      contactCreateSuccess: 'Created contact successfully',
      contactUpdateSuccess: 'Updated contact successfully',
      cancelRenew: 'Cancel Renewal',
      keepRenew: 'Keep Renewal',
      subCancelRenew:
        'You are about to turn off auto-renewal for this number. This action is irreversible. Are you sure you want to continue?',
    },
    unverified: 'Unverified',
    date: 'Date',
    time: 'Time',
    hour: 'Hour',
    minute: 'Minute',
    min: 'Min',
    note: 'Note',
    more: 'More',
    province: 'Province',
    company: 'Company',
    seconds: 'Seconds',
    duration: 'Duration',
    big360: 'Big360',
    duplicatePhoneNumber: 'Move to Contact Details of the duplicate phone number',
    restoreArchivePhone:
      'This phone number is duplicated with one in Archived. Do you want to restore it?',
    pic: 'PIC',
    demographic: 'Demographic',
    age: 'Age',
    gender: 'Gender',
    dob: 'DOB',
    birthMonth: 'Birth Month',
    city: 'City',
    status: 'Status',
    sort: 'Sort',
    verify: 'Verify',
    verifying: 'Verifying',
    contactFrequency: 'Call Count',
    verifyNumber: 'Verify Number',
    month: 'Month',
    day: 'Day',
    year: 'Year',
    name: 'Name',
    phoneNumber: 'Phone Number',
    email: 'Email',
    address: 'Address',
    ward: 'Ward',
    district: 'District',
    position: 'Position',
    price: 'Price',
    filterQuantity: 'Filter Quantity',
    contactHistory: 'Contact History',
    cityOrProvince: 'City/Province',
    available: 'Available',
    reported: 'Reported',
    fullName: 'Full Name',
    confirmed: 'Confirmed',
    unconfirm: 'Unconfirm',
    campaign: 'Campaign',
    description: 'Description',
    callOut: 'Call Out',
    spam: 'Spam',
    rowPage: 'Rows per page',
    invalid: 'Invalid',
    valid: 'Valid',
    unverify: 'Unverify',
    solid: 'Solid',
    gradient: 'Gradient',
    not_exist_bought_phone: 'Not exist bought phone or reported',
    pls_click_btn_select: 'Please click button select to view contact',
    back_to_bought_phones: 'Back to bought phones',
    setReminder: 'Set Reminder',
    history: 'History',
    content: 'Content',
    missedCall: 'Missed Call',
    completedCall: 'Completed Call',
    facebookBroadcast: 'Facebook Broadcast',
    marketingEmail: 'Marketing Email',
    zaloBroadcast: 'Zalo Broadcast',
    all: 'All',
    call: 'Call',
    zalo: 'Zalo',
    facebook: 'Facebook',
    facebookTitlePageBreadcrumb: 'Facebook',
    tiktokTitlePageBreadcrumb: 'Tiktok Ads',
    tagColor: 'Tag’s color',
    calling: 'Calling',
    recording: 'Recording',
    callScript: 'Call Script',
    failToConnectHotline: "Can't connect Switchboard",
    noOptions: 'No options',
    noScript: 'No script to show',
    cleanUp: 'Clean Up',
    cleaningUp: 'Cleaning Up',
    quickNote: 'Quick Note',
    saved: 'Saved',
  },
  contactDetail: {
    personalInformation: 'Personal Information',
    contactInformation: 'Contact Information',
  },
  segment: {
    title: 'Segment',
    datasetTitle: 'Dataset Segment',
    audienceTitle: 'Audience Segment',
    createSegment: 'Create Segment',
    updateSegment: 'Update Segment',
    callScript: 'Call Script',
    enterSegmentName: 'Enter Segment’s Name',
    description: 'Description',
    segmentName: 'Segment’s Name',
    totalContact: 'Total Contact',
    totalContacts: 'Total Contacts',
    createDate: 'Create Date',
    selectSegment: 'Select Segment',
    descriptionPage: 'Easily create and manage campaigns synced with your CRM.',
    titleDeleteSegment: 'Delete Segment',
    deleteSegmentDescription:
      'You are about to delete this segment. This action cannot be undone. Are you sure you want to proceed?',
    segmentMovedToTrash: 'Segment moved to trash',
    segmentCreateSuccess: 'Created segment successfully',
    segmentUpdateSuccess: 'Updated segment successfully',
    addMoreContact: 'Add more contact',
  },
  contactList: {
    uploadSteps: {
      step1: {
        title: 'Step 1: File Header Mapping',
        description:
          ' Your file headers must match the system format. If there is a mismatch, please map them manually before proceeding.',
      },
      step2: {
        title: 'Step 2:  Phone Number Validation',
        description:
          'The system has detected invalid phone numbers (missing or incorrect format). You can edit or remove them before uploading.',
      },
      step3: {
        title: 'Step 3: File Naming Required',
        description: 'Please enter a file name before uploading.',
      },
    },
    searchSegmentPlaceHolder: 'Search for segment name',
    createSegmentSuccess: 'Created segment successfully',
    updateSegmentSuccess: 'Updated segment successfully',
    mustSelectHotLine: 'You must select a phone number to use this feature',
    expiredPhone: 'Your phone number has expired. Please rent a new number to continue.',
    unlockCall: 'Unlock Call Feature',
    rentNumber: 'Rent Number',
    credit: 'Credit',
    yourCredit: 'Your Credit',
    dateExpiredDanger: 'Your number has expired. You have 3 days to restore.',
    dateExpiredWarn: 'Your number has 3 days remaining',
    phoneNumber: 'Phone Number',
    dueDate: 'Due Date',
    timeRemaining: 'Time Remaining',
    expired: 'Expired',
    switchboard: 'Switchboard',
    status: 'Status',
    descriptionPage: 'Easily create and manage campaigns synced with your CRM.',
    titlePage: 'Contact List',
    titleDatasetPage: 'Dataset',
    titlePageBreadcrumb: 'Contact List',
    totalContactOverdue: 'Total Contact Overdue',
    titleHistory: 'Contact History',
    uploadContact: 'Upload Contact',
    addNewContact: 'Add new contact',
    maximumFile: 'Maximum file size is 25MB',
    dropFile: 'Drop your file here or',
    browse: 'browse',
    fromComputer: 'from your computer',
    upload: 'Upload',
    downloadTemplate: 'Download Template',
    selectDataOptions: 'Click on a cell to edit and press Enter to confirm the changes',
    dataOption: 'Data Option',
    selectedOption: 'Selected Option',
    titleAddContact: 'Add Contact',
    addFormBig360: 'Add from Big360',
    titleEditContact: 'Edit Contact',
    uploadSuccess: 'Upload successfully',
    report_phone: 'Report phone number',
    confirm_report: 'Are you sure you want to report this phone number?',
    not_undone: 'This action cannot be undone.',
    titleDeleteContact: 'Remove Contact',
    deleteContactDescription:
      'You’re about to remove these contacts from your Contact List.\n' +
      'They will be moved to the archive. Do you want to continue?',
    contactMovedToTrash: 'Contact moved to archive',
    trashTitlePage: 'Trash',
    archivedTitlePage: 'Archived',
    trashDescriptionPage:
      'Contacts that have been in Trash more than 30 days will be deleted permanently',
    contactsRestored: 'Contacts restored',
    yourChangeHasBeenSavedSuccessfully: 'Your change has been saved successfully',
    emptyTrash: 'Empty Trash',
    emptyTrashDescription: 'Are you sure you want to empty trash?',
    fileFormatWasWrong: 'File format was wrong. You can check file download to fix that!!!',
    contactCreateSuccess: 'Created contact successfully',
    contactUpdateSuccess: 'Updated contact successfully',
  },
  placeHolder: {
    contactName: 'Enter contact name',
    phoneNumber: 'Enter Phone Number',
    email: 'Enter email',
    company: 'Enter company',
    segment: 'enter segment',
    selectSegment: 'Select segment',
    address: 'Enter address',
    position: 'Enter position',
    cityOrProvince: 'Enter city/province',
    dob: 'Birth Month',
    gender: 'Gender',
    search_phone: 'Search phone number',
    enterQuantity: 'Enter Quantity',
    selectTypeBtn: 'Select button type',
  },
  notification: {
    title: 'Notification',
    reminder: 'Reminder',
    zalo: 'Zalo ',
    verify: 'Verify',
  },
  filter: {
    age: 'Age',
    gender: 'Gender',
    birthMonth: 'Birth Month',
    segment: 'Segment',
    location: 'Location',
    status: 'Status',
    frequencyMin: 'Call Count Min',
    frequencyMax: 'Call Count Max',
  },
  footer: footer,
  contactHistory: contactHistory,
  verify: verify,
  report: reportContact,
  howToConnect: howtoConnect,
  getPhoneNumber: getPhoneNumber,
  limitAlert: limitAlert,
  email: email,
  segmentLog: segmentLog,
  tiktokAds: tiktokAds,
  googleAds: googleAds,
  audience: audience,
});
