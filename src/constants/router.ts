export const ROOT_PATH = import.meta.env.REACT_APP_ROOT_PATH;

export enum CONTACT_ROUTER {
  CONTACT = 'contact',
  SEGMENT = 'contact/segment',
  CONTACT_DETAIL = 'contact-detail',
  SEGMENT_DETAIL = 'contact/segment/detail',
}

export enum FACEBOOK_ROUTER {
  '' = 'facebook',
}

export enum TIKTOK_ADS_ROUTER {
  '' = 'tiktok-ads',
  AUTHORIZE_FAILED = 'tiktok-ads/authorize-failed',
  PERMISSION_FAILED = 'tiktok-ads/permission-failed'
}


export enum GOOGLE_ADS_ROUTER {
  '' = 'google-ads',
  AUTHORIZE_FAILED = 'google-ads/authorize-failed',
  PERMISSION_FAILED = 'google-ads/permission-failed'
}

export enum GET_PHONE_ROUTER {
  GET_PHONE_NUMBER = 'contact/get-phone-number',
}

export enum ZALO_ROUTER {
  zalo = 'zalo',
  zns = 'zns',
  campaign = 'campaign',
  createTemplate = 'create-template',
  howToConnect = 'zalo/how-to-connect-to-zalo-oa',
}

export enum EMAIL_ROUTER {
  email = 'email',
  email_campaign = 'create-campaign',
  design = 'design',
  create_sender = 'create-sender',
  verifySender = 'verify-sender',
  multiple_config = 'multiple-config',
  sender = 'sender',
}

export enum EMAIL_ROUTER_CONFIG {
  SENDER = 'sender',
  DOMAINS = 'domains',
  DEDICATED_IPS = 'dedicated-ips',
}

export const ROOT_ROUTE = {
  path: `/`,
  contact: {
    '': `${CONTACT_ROUTER.CONTACT}`,
    segment: `${CONTACT_ROUTER.SEGMENT}`,
    phone: `${GET_PHONE_ROUTER.GET_PHONE_NUMBER}`,
    detail: `${CONTACT_ROUTER.CONTACT_DETAIL}`,
    segmentDetail: `${CONTACT_ROUTER.SEGMENT_DETAIL}`,
  },
  'get-phone-number': {
    '': GET_PHONE_ROUTER.GET_PHONE_NUMBER,
  },
  facebook: {
    '': FACEBOOK_ROUTER[''],
  },
  tiktok: {
    '': TIKTOK_ADS_ROUTER[''],
    AUTHORIZE_FAILED: TIKTOK_ADS_ROUTER.AUTHORIZE_FAILED,
    PERMISSION_FAILED: TIKTOK_ADS_ROUTER.PERMISSION_FAILED,
  },
  gooogle: {
    '': GOOGLE_ADS_ROUTER[''],
    AUTHORIZE_FAILED: GOOGLE_ADS_ROUTER.AUTHORIZE_FAILED,
    PERMISSION_FAILED: GOOGLE_ADS_ROUTER.PERMISSION_FAILED,
  },
  zalo: {
    '': `${ZALO_ROUTER.zalo}`,
    campaign: `${ZALO_ROUTER.zalo}/${ZALO_ROUTER.campaign}`,
    zns: `${ZALO_ROUTER.zalo}/${ZALO_ROUTER.zns}`,
    createTemplate: `${ZALO_ROUTER.zalo}/${ZALO_ROUTER.zns}/${ZALO_ROUTER.createTemplate}`,
    howToConnect: `${ZALO_ROUTER.zalo}/${ZALO_ROUTER.howToConnect}`,
  },
};

export const ROUTE_NAME_BIG360 = {
  yourSegment: `/your-segments`,
};
