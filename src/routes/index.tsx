import { Navigate, Outlet, useRoutes } from 'react-router-dom';
import useContactRouter from './useContactRouter';
import { ROOT_PATH, ROOT_ROUTE } from '@/constants/router';
import useFacebookRouter from './useFacebookRouter';
import useZaloRouter from './useZaloRouter';
import usePushNotice from '@/hooks/usePushNotice';
import NotFound from '@/pages/NotFound';
import useTiktokAdsRouter from './useTiktokAdsRouter';
import useGoogleAdsRouter from './useGoogleAdsRouter';
// import useEmailRouter from './useEmailRouter';

const AppRouter = () => {
  usePushNotice();

  const contactRoutes = useContactRouter();
  const facebookRoutes = useFacebookRouter();
  const tiktokAdsRoutes = useTiktokAdsRouter();
  const googleAdsRoutes = useGoogleAdsRouter();
  const zaloRoutes = useZaloRouter();
  // const emailRoutes = useEmailRouter();

  const deployTag = import.meta.env.REACT_APP_DEPLOY_TAG === 'development';

  const routes = [
    ...contactRoutes,
    ...facebookRoutes,
    ...tiktokAdsRoutes,
    ...(deployTag ? googleAdsRoutes : []),
    ...zaloRoutes,
    // ...emailRoutes,  // Uncomment when emailRoutes is needed
  ];

  return useRoutes([
    {
      path: '/',
      element: <Outlet />,
      children: [
        {
          path: '/',
          element: <Navigate to={`${ROOT_PATH}/${ROOT_ROUTE.contact['']}`} />,
        },
        ...routes
      ],
    },
    {
      path: '*',
      element: <NotFound />,
    },
  ]);
};

export default AppRouter;
