import { SuspenseWrapper } from '@/components/SuspenseWrap';
import { GOOGLE_ADS_ROUTER } from '@/constants/router';
import { lazy } from 'react';
import { RouteObject } from 'react-router-dom';
import { GoogleAuthProvider } from '@/pages/GoogleAds/context/GoogleAuthContext';
import { AccessDenied } from '@/pages/GoogleAds/components/AccessDenied';
import Breadcrumb from '@/components/Breadcrumb';
import { TitleGoogle } from '@/pages/GoogleAds/components/TitleGoogle';
import { AuthorizeFailed } from '@/pages/GoogleAds/components/AuthorizeFailed';

const GooglePage = lazy(() => import('@/pages/GoogleAds'));

const useGoogleAdsRouter = (): RouteObject[] => [
  {
    path: GOOGLE_ADS_ROUTER[''],
    element: (
      <SuspenseWrapper
        component={
          <GoogleAuthProvider>
            <GooglePage />
          </GoogleAuthProvider>
        }
      />
    ),
  },
  {
    path: GOOGLE_ADS_ROUTER.PERMISSION_FAILED,
    element: (
      <SuspenseWrapper
        component={
          <GoogleAuthProvider>
            <Breadcrumb />
            <TitleGoogle />
            <AccessDenied />
          </GoogleAuthProvider>
        }
      />
    ),
  },
  {
    path: GOOGLE_ADS_ROUTER.AUTHORIZE_FAILED,
    element: (
      <SuspenseWrapper
        component={
          <GoogleAuthProvider>
            <Breadcrumb />
            <TitleGoogle />
            <AuthorizeFailed />
          </GoogleAuthProvider>
        }
      />
    ),
  },
];

export default useGoogleAdsRouter;
