import { toast as sonnerToast, Toaster } from 'sonner';
import { cn } from '@/lib/utils';
import {
  RiAlertLine,
  RiCheckboxCircleLine,
  RiInformation2Line,
  RiSpamLine,
} from '@remixicon/react';
import React from 'react';

type Status = 'info' | 'success' | 'error' | 'warning';

interface ToastProps {
  status?: Status;
  title: React.ReactNode;
  onClick?: () => void;
  className?: string;
}

export const Toast = () => {
  return (
    <Toaster
      position="top-right"
      toastOptions={{
        className: 'right-0 w-max',
      }}
    />
  );
};

export function toast({ status = 'success', title, onClick }: ToastProps) {
  sonnerToast.custom(() => (
    <div
      onClick={onClick}
      className={cn(
        'flex w-full items-center justify-start rounded-xl border p-2 shadow-md gap-x-2.5',
        classesStatus(status),
        onClick && 'cursor-pointer',
      )}
    >
      {toastIcon(status)}
      <div className="flex flex-col">
        <p className="text-sm text-start">{title}</p>
      </div>
    </div>
  ));
}

const toastIcon = (status: Status) => {
  switch (status) {
    case 'error':
      return <RiSpamLine className="flex-shrink-0" color="#BF1616" size={24} />;
    case 'info':
      return <RiInformation2Line color="#194A8F" className="flex-shrink-0" size={24} />;
    case 'warning':
      return <RiAlertLine color="#92470E" size={24} className="flex-shrink-0" />;
    case 'success':
      return <RiCheckboxCircleLine color="#205B2B" size={24} className="flex-shrink-0" />;
    default:
      return <></>;
  }
};

const classesStatus = (status: Status) => {
  switch (status) {
    case 'error':
      return 'border-big360Color-danger-400 bg-big360Color-danger-100 text-big360Color-danger-500';
    case 'success':
      return 'border-big360Color-success-400 bg-big360Color-success-50 text-big360Color-success-500';
    default:
      return 'border-border bg-background text-foreground';
  }
};
