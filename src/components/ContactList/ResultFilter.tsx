import React from 'react';
import { IOptions, IResultFilter, ISelectFilter } from '@/types/contactList';
import { RiDeleteBin6Line } from '@remixicon/react';
import { Button } from '@/components/ui/button';
import { useTranslation } from 'react-i18next';
import { useSearchParams } from 'react-router-dom';
import { genderSelectOptions, LABEL } from '@/utils/constants';
import { filterEmptyParams, omitKeyInObjectArr, updateKeySearchParams } from '@/utils/helper';

const n = (key: keyof ISelectFilter) => key;
const ResultFilter: React.FC<IResultFilter> = ({ ...props }: IResultFilter) => {
  const { total = 0, selectFilter, setSelectFilter } = props;
  const { t } = useTranslation();
  const [, setSearchParams] = useSearchParams();

  const formatParamsForApi = (
    selectFilter: Record<string, string[]>,
    genderSelectOptions: IOptions[],
  ) => {
    const demographicFilter = selectFilter[n('demographic')] || [];
    const ageFilter = demographicFilter
      .filter((item) => !genderSelectOptions.some((option) => option.value === item))
      .join(',');

    const genderFilter = demographicFilter
      .filter((item) => genderSelectOptions.some((option) => option.value === item))
      .join(',');

    return {
      age__in: ageFilter,
      gender__in: genderFilter,
      ...updateKeySearchParams(omitKeyInObjectArr(selectFilter, [LABEL.demographic.toLowerCase()])),
    };
  };

  const handleSearch = () => {
    if (!selectFilter) {
      return;
    }
    const paramSearch = formatParamsForApi(selectFilter, genderSelectOptions);
    const filteredParams: Record<string, string> = filterEmptyParams(paramSearch);
    if (Object.keys(filteredParams).length === 0) {
      setSelectFilter({});
    }
    setSearchParams(filteredParams);
  };

  return (
    <>
      <div className="flex justify-between items-center mt-3">
        <p className="flex gap-1 text-sm font-normal">
          <span className="font-medium">{Number(total).toLocaleString()}</span>
          <span>
            {t('common.resultsFound', {
              symbol: Number(total) > 1 ? 's' : '',
            })}
          </span>
        </p>
        <div className="flex gap-4">
          <Button
            className="h-[40px] w-[108px] bg-brand text-background-foreground font-medium text-base rounded-xl hover:bg-brand-secondary hover:text-background-foreground"
            onClick={handleSearch}
          >
            {t('common.button.apply')}
          </Button>
          <button
            onClick={() => {
              setSelectFilter({});
              setSearchParams({});
            }}
            className="flex gap-1 items-center font-normal text-delete"
          >
            <RiDeleteBin6Line />
            {t('common.button.clearAll')}
          </button>
        </div>
      </div>
    </>
  );
};
export default ResultFilter;
