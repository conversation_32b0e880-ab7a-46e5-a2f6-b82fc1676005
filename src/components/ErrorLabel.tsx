import { cn } from '@/lib/utils';
import { RiInformation2Line } from '@remixicon/react';

type Props = {
  content?: string;
  className?: string;
};

const ErrorLabel = ({ content, className }: Props) => {
  if (!content) return null;
  return (
    <div className={cn('flex items-center gap-1 text-xs text-[#F53E3E] leading-[18px]', className)}>
      <RiInformation2Line size={16} />
      {content}
    </div>
  );
};
export default ErrorLabel;
