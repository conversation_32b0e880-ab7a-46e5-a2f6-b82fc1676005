import ConnectModal from '@/components/ConnectModal';
import { useMutation } from '@tanstack/react-query';
import SelectAccountModal from '@/components/SelectAccountModal';
import { useEffect, useState } from 'react';
import { googleOauthApi } from '@/apis/googleOauth';
import { useGoogleContext } from '@/pages/GoogleAds/context/GoogleAuthContext';
import { useTranslation } from 'react-i18next';
import SelectAccountPopup from '@/pages/GoogleAds/components/SelectAccountPopup';
import { ROOT_PATH, ROOT_ROUTE } from '@/constants/router';

const EmptyGoogleDataView = () => {
  const { t } = useTranslation();
  const [showModal, setShowModal] = useState(false);

  const { loading, isAccountSelected, isLogin } = useGoogleContext();
  const mutation = useMutation({
    mutationFn: googleOauthApi.getGoogleOauthUrl,
    onSuccess: (data) => {
      window.location.href = data?.data?.auth_link ?? '/';
    },
  });
  const handleLogin = () => mutation.mutate(`${ROOT_PATH}/${ROOT_ROUTE.gooogle['']}`);

  useEffect(() => {
    if (isLogin && !isAccountSelected) {
      setShowModal(true);
    }
  }, [isLogin, isAccountSelected]);

  return (
    <div className="flex flex-col items-center gap-6 my-32">
      {!isLogin && (
        <ConnectModal
          disabled={mutation.isSuccess}
          onConnect={handleLogin}
          value={'Google'}
          loading={mutation.isPending}
        />
      )}
      {!isAccountSelected && isLogin && (
        <SelectAccountModal
          disabled={mutation.isSuccess}
          onSelect={() => setShowModal(true)}
          value={'Google Ads'}
          loading={loading}
          buttonText={t('common.button.selectGoogleAccount')}
        />
      )}
      {showModal && <SelectAccountPopup open={showModal} setOpen={setShowModal} />}
    </div>
  );
};

export default EmptyGoogleDataView;
