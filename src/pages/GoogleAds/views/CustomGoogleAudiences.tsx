import { useState } from 'react';
import DataTable from '@/components/table/DataTable';
import { TFilterAudienceTiktok } from '@/types/facebook';
import { ICustomAudienceResponse } from '@/types/google';
import FilterPanel from '@/pages/GoogleAds/components/FilterPanel';
import audienceCol from '@/pages/GoogleAds/components/Column/audienceCol';
import { useGoogleContext } from '@/pages/GoogleAds/context/GoogleAuthContext';
import { formatDateYYYYMMDD } from '@/utils/helper';
import { IAudienceDetail } from '@/types/audience';

interface ICustomAudiences {
  filterPayload: TFilterAudienceTiktok;
  setFilterPayload: (value: TFilterAudienceTiktok) => void;
  loading?: boolean;
  googleCustomAudience: ICustomAudienceResponse;
  onOpenHistoryModal: (detail: IAudienceDetail) => void;
  onOpenUpdateModal: (detail: IAudienceDetail) => void;
}

const CustomGoogleAudiences = ({
  filterPayload,
  setFilterPayload,
  loading,
  googleCustomAudience,
  onOpenHistoryModal,
  onOpenUpdateModal,
}: ICustomAudiences) => {
  const { adsAccount } = useGoogleContext();
  const [popoverStates, setPopoverStates] = useState<Record<string, boolean>>({});
  const [refreshingRows, setRefreshingRows] = useState<Set<string>>(new Set());

  return (
    <div className="flex flex-col">
      <FilterPanel
        setFilterPayload={(value) => {
          setFilterPayload({
            ...filterPayload,
            ...value,
            date_created_from: formatDateYYYYMMDD(value?.date_created_from ?? '', '-'),
            date_created_to: formatDateYYYYMMDD(value?.date_created_to ?? '', '-'),
          });
        }}
        filterPayload={filterPayload}
      />
      <DataTable
        className="h-[468px]"
        data={googleCustomAudience.items}
        columns={audienceCol({
          act: adsAccount?.ad_account_id || '',
          setIdActive: () => {},
          onOpenHistoryModal,
          onOpenUpdateModal,
          filterPayload,
        })}
        total={googleCustomAudience.count}
        loading={loading}
        pagination={{ pageSize: filterPayload.limit, currentPage: filterPayload.page }}
        setPagination={(value) => {
          setFilterPayload({ ...filterPayload, limit: value.pageSize, page: value.currentPage });
        }}
        popoverStates={popoverStates}
        setPopoverStates={setPopoverStates}
        refreshingRows={refreshingRows}
        setRefreshingRows={setRefreshingRows}
      />
    </div>
  );
};
export default CustomGoogleAudiences;
