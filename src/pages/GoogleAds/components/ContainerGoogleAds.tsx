import { useEffect, useState } from 'react';
import { TFilterAudienceTiktok } from '@/types/facebook';
import { useGoogleContext } from '@/pages/GoogleAds/context/GoogleAuthContext';
import HeaderGoogle from '@/pages/GoogleAds/components/HeaderGoogle';
import { ICustomAudienceResponse } from '@/types/google';
import { get } from '@/apis/apiHelper';
import { ENDPOINTS } from '@/apis/endpoints';
import { QUERY_KEY } from '@/utils/constants';
import { useQuery } from '@tanstack/react-query';
import { IAudienceDetail } from '@/types/audience';
import CustomGoogleAudiences from '@/pages/GoogleAds/views/CustomGoogleAudiences';

interface ContainerGoogleAdsProps {
  onOpenHistoryModal: (detail: IAudienceDetail) => void;
  onOpenUpdateModal: (detail: IAudienceDetail) => void;
}

const ContainerGoogleAds = ({ onOpenHistoryModal, onOpenUpdateModal }: ContainerGoogleAdsProps) => {
  const [filterPayload, setFilterPayload] = useState<TFilterAudienceTiktok>({
    search: '',
    page: 1,
    limit: 10,
    date_created_from: '',
    date_created_to: '',
    platform: 'GG',
  });
  const { adsAccount } = useGoogleContext();
  const [googleCustomAudience, setGoogleCustomAudience] = useState<ICustomAudienceResponse>({
    items: [],
    count: 0,
  });

  const {
    data: customAudienceResponse,
    isLoading: loadingContact,
    isFetching: isFetchingContact,
    isPending: isPendingContact,
  } = useQuery({
    queryKey: [QUERY_KEY.GOOGLE_AUDIENCE, filterPayload, adsAccount?.ad_account_id],
    enabled: !!adsAccount?.ad_account_id,
    staleTime: 1000,
    queryFn: () =>
      get({
        endpoint: ENDPOINTS.custom_audience[''],
        params: {
          ...filterPayload,
          ad_account_id: adsAccount?.ad_account_id,
          order_by: '-date_created',
        },
      }).then((res) => {
        if (res?.data?.code === 1001) {
          return {
            items: [],
            count: 0,
          };
        }
        return res.data?.data as ICustomAudienceResponse;
      }),
  });

  useEffect(() => {
    if (!!customAudienceResponse) {
      setGoogleCustomAudience(customAudienceResponse);
    }
  }, [customAudienceResponse]);

  return (
    <>
      <HeaderGoogle
        filterPayload={filterPayload}
        setFilterPayload={setFilterPayload}
        ad_Account_id={adsAccount?.ad_account_id ?? ''}
      />

      <CustomGoogleAudiences
        filterPayload={filterPayload}
        setFilterPayload={setFilterPayload}
        loading={loadingContact || isFetchingContact || isPendingContact}
        googleCustomAudience={googleCustomAudience}
        onOpenHistoryModal={onOpenHistoryModal}
        onOpenUpdateModal={onOpenUpdateModal}
      />
    </>
  );
};

export default ContainerGoogleAds;
