import React, { useEffect, useState, useMemo } from 'react';
// import { useTranslation } from 'react-i18next';
import DataTable from '@/components/table/DataTable';
import { ColumnDef } from '@tanstack/react-table';
import { IGoogleHistoryResponse, IGoogleHistoryItem } from '@/types/google';
import historyGoogleCol from '@/pages/GoogleAds/components/Column/historyGoogleCol';
import { TPagination } from '@/types/table';
import { Box } from '@/components/Box';
import { useQuery } from '@tanstack/react-query';
import { PAGE_SIZE, QUERY_KEY } from '@/utils/constants';
import { get } from '@/apis/apiHelper';
import { ENDPOINTS } from '@/apis/endpoints';
import { useTranslation } from 'react-i18next';
import { IAudienceDetail } from '@/types/audience';

interface IHistoryGoogle {
  jobId: number;
  totalContact: number;
  onUpdateAudienceDetail?: (jobId: number, updatedDetail: IAudienceDetail) => void;
}

const HistoryGoogle: React.FC<IHistoryGoogle> = ({ jobId, totalContact, onUpdateAudienceDetail }) => {
  const { t } = useTranslation();
  const [historyGoogleAds, setHistoryGoogleAds] = useState<IGoogleHistoryResponse>({
    count: 0,
    items: [],
  });
  const [pagination, setPagination] = useState<TPagination>({
    currentPage: 1,
    pageSize: PAGE_SIZE,
  });

  const { data: audienceDetailResponse } = useQuery({
    enabled: !!jobId,
    queryKey: [QUERY_KEY.GOOGLE_AUDIENCE_DETAIL, jobId],
    queryFn: () =>
      get({
        endpoint: ENDPOINTS.custom_audience.detail(jobId.toString()),
      }),
    staleTime: 0,
  });

  const { data: historyResponse, isLoading: loadingContactSegment } = useQuery({
    enabled: !!jobId,
    queryKey: [QUERY_KEY.GOOGLE_HISTORY, pagination, jobId],
    queryFn: () =>
      get<IGoogleHistoryItem>({
        endpoint: ENDPOINTS.custom_audience.history(jobId),
        params: {
          page: pagination.currentPage,
          limit: pagination.pageSize,
        },
      }),
  });

  const actualTotalContacts = useMemo(() => {
    if (audienceDetailResponse?.data?.data) {
      const audienceDetail = audienceDetailResponse.data.data as unknown as IAudienceDetail;

      // Update the audience detail in the table if callback is provided
      if (onUpdateAudienceDetail) {
        onUpdateAudienceDetail(jobId, audienceDetail);
      }

      return audienceDetail.total_records || totalContact;
    }
    return totalContact;
  }, [audienceDetailResponse, totalContact, onUpdateAudienceDetail, jobId]);

  useEffect(() => {
    if (historyResponse?.data?.data) {
      setHistoryGoogleAds({
        items: historyResponse.data.data.items || [],
        count: historyResponse.data.data.count || 0,
      });
    }
  }, [historyResponse?.data]);

  return (
    <div>
      <DataTable
        data={historyGoogleAds.items}
        columns={historyGoogleCol() as ColumnDef<IGoogleHistoryItem>[]}
        total={historyGoogleAds.count}
        loading={loadingContactSegment}
        pagination={pagination}
        setPagination={setPagination}
        className="h-[352px] max-w-[920px]"
      />
      <Box className="mb-1 mt-3">
        <p className="font-medium">{t('common.totalSegments')}:</p>
        <span className="font-medium">{Number(historyGoogleAds.count ?? 0).toLocaleString()}</span>
      </Box>
      <Box>
        <p className="font-medium">{t('common.totalContacts')}:</p>
        <span className="font-semibold text-lg">{Number(actualTotalContacts ?? 0).toLocaleString()}</span>
      </Box>
    </div>
  );
};

export default HistoryGoogle;
