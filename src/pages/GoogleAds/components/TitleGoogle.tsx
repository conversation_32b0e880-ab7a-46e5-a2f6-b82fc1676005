import { useTranslation } from 'react-i18next';

export const TitleGoogle = () => {
  const { t } = useTranslation();
  return (
    <div className="text-left mb-4">
      <div className="flex-1 h-[64px]">
        <h3 className="text-xl font-semibold mb-2">{t('googleAds.title')}</h3>
        <p className="text-base font-normal text-tertiary-foreground tracking-[0.2px]">
          {t('googleAds.description')}
        </p>
      </div>
    </div>
  );
};
