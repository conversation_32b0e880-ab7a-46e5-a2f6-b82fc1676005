import { Button } from '@/components/ui/button';
import { useSegmentContext } from '@/pages/context/SegmentContext';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { cn } from '@/lib/utils';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useTranslation } from 'react-i18next';
import LabelCustom from '@/components/Label';
import { NoData } from '@/components/NoData';
import { IAudienceDetail } from '@/types/audience';
import { useInfiniteQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { QUERY_KEY } from '@/utils/constants';
import { get } from '@/apis/apiHelper';
import { IGoogleHistoryItem, IGoogleHistoryResponse } from '@/types/google';
import { ENDPOINTS } from '@/apis/endpoints';
import { RiLoader2Line } from '@remixicon/react';
import { googleOauthApi } from '@/apis/googleOauth';
import { toast } from '@/hooks/use-toast';
import { LimitUpdateAudience } from '@/components/LimitUpdateAudience';

type Props = {
  detail: IAudienceDetail;
  onClose?: () => void;
};

const UpdateCustomAudience: React.FC<Props> = ({ detail, onClose }: Props) => {
  const { audience_name, job_id } = detail;
  const { items } = useSegmentContext();
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const loadMoreRef = useRef<HTMLDivElement>(null);
  const [openModal, setOpenModal] = useState<boolean>(false);
  const [isScrolling, setIsScrolling] = useState<boolean>(false);
  const [openWarning, setOpenWarning] = useState<boolean>(false);

  const [payload, setPayload] = useState<{
    job_id: number;
    segment_update_id: string;
  }>({ job_id: job_id, segment_update_id: '' });

  useEffect(() => {
    if (onClose) {
      setPayload({ job_id: job_id, segment_update_id: '' });
    }
  }, [onClose, job_id]);

  const newOptions = items.reduce<{ label: string; value: string; count: number }[]>(
    (acc, item) => {
      if (item.contact_quantity > 0) {
        acc.push({
          label: item.name,
          value: item.id,
          count: item.contact_quantity,
        });
      }
      return acc;
    },
    [],
  );

  const { data, fetchNextPage, hasNextPage, isFetchingNextPage, isLoading, isError } =
    useInfiniteQuery({
      queryKey: [QUERY_KEY.GOOGLE_LIST_SEGMENT, 'history', job_id],
      enabled: !!job_id && (openModal || !!onClose), // Enable if internal modal is open OR external modal is used
      staleTime: 1000, // 1 second
      refetchOnWindowFocus: false,
      refetchOnMount: false,
      queryFn: async ({ pageParam = 1 }) => {
        const response = await get<IGoogleHistoryResponse>({
          endpoint: ENDPOINTS.custom_audience.history(job_id),
          params: {
            page: pageParam,
            limit: 20,
          },
        });
        /* eslint-disable @typescript-eslint/no-explicit-any */
        return response.data?.data as any as IGoogleHistoryResponse;
      },
      getNextPageParam: (lastPage, allPages) => {
        if (!lastPage?.items || lastPage.items.length < 20) {
          return undefined;
        }
        return allPages.length + 1;
      },
      initialPageParam: 1,
    });

  const allHistoryItems = data?.pages.flatMap((page) => page?.items || []) || [];

  useEffect(() => {
    const loadMoreElement = loadMoreRef.current;
    if (!loadMoreElement || !hasNextPage || isFetchingNextPage) {
      return;
    }

    const observer = new IntersectionObserver(
      (entries) => {
        const [entry] = entries;
        if (entry.isIntersecting && hasNextPage && !isFetchingNextPage && !isScrolling) {
          setIsScrolling(true);
          fetchNextPage().finally(() => {
            setTimeout(() => setIsScrolling(false), 300);
          });
        }
      },
      {
        threshold: 0.1,
        rootMargin: '50px',
      },
    );

    observer.observe(loadMoreElement);

    return () => {
      observer.unobserve(loadMoreElement);
    };
  }, [fetchNextPage, hasNextPage, isFetchingNextPage, isScrolling]);

  // Handle scroll to load more data with spam prevention
  const handleScroll = useCallback(
    (e: React.UIEvent<HTMLDivElement>) => {
      const target = e.currentTarget;

      if (!target || isScrolling || isFetchingNextPage || !hasNextPage) {
        return;
      }

      const { scrollTop, scrollHeight, clientHeight } = target;
      const isNearBottom = scrollTop + clientHeight >= scrollHeight - 50;

      if (isNearBottom) {
        setIsScrolling(true);
        fetchNextPage().finally(() => {
          setTimeout(() => setIsScrolling(false), 300);
        });
      }
    },
    [fetchNextPage, hasNextPage, isFetchingNextPage, isScrolling],
  );

  const renderHistoryItem = (item: IGoogleHistoryItem) => {
    return (
      <p key={item.id} className="text-xs text-big360Color-neutral-700 mb-1">
        {item.segment_info?.name || 'Unknown Segment'}
      </p>
    );
  };

  const mutationUpdate = useMutation({
    mutationFn: googleOauthApi.updateAudience,
    onSuccess: () => {
      setOpenModal(false);
      if (onClose) {
        onClose();
      }
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEY.GOOGLE_AUDIENCE],
      });
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEY.GOOGLE_AUDIENCE_DETAIL, job_id],
      });
      toast({
        title: t('audience.updateCustomAudienceSuccess'),
        status: 'success',
      });
    },
    onError: (error: any) => {
      toast({
        title: error?.response?.data?.message || t('audience.updateCustomAudienceFailed'),
        status: 'error',
      });
    },
  });

  const handleSubmit = () => {
    const { job_id, segment_update_id } = payload;

    const updatedAt = new Date(detail.updated_at);
    const currentTime = new Date();
    const timeDifferenceInHours = (currentTime.getTime() - updatedAt.getTime()) / (1000 * 60 * 60);

    if (timeDifferenceInHours < 1) {
      if (onClose) {
        setOpenWarning(true);
      } else {
        setOpenModal(false);
        setOpenWarning(true);
      }
      return;
    }

    mutationUpdate.mutate({ job_id, segment_update_id });
  };

  return (
    <>
      <div className="flex flex-col gap-4">
        <div className="flex flex-col">
          <LabelCustom className="mb-2" label={t('audience.customAudienceName')} />
          <div className="flex border-input bg-transparent md:text-sm text-big360Color-neutral-950 border h-10 w-full px-3 py-2 rounded-xl text-sm">
            {audience_name}
          </div>
        </div>
        <div className="h-[170px] rounded-xl bg-big360Color-neutral-50 border-big360Color-neutral-100 mb-2 p-2">
          <p className="mb-1 text-xs text-big360Color-neutral-950 font-medium">
            {t('audience.included')}
          </p>
          <div
            ref={scrollContainerRef}
            className="overflow-auto h-[130px]"
            id={'history-google'}
            onScroll={handleScroll}
          >
            {isLoading ? (
              <div className="flex justify-center items-center h-full">
                <RiLoader2Line className="animate-spin" size={20} />
              </div>
            ) : isError ? (
              <div className="flex justify-center items-center h-full text-red-500 text-sm">
                {t('common.error.loadData')}
              </div>
            ) : allHistoryItems.length === 0 ? (
              <NoData />
            ) : (
              <>
                {allHistoryItems.map(renderHistoryItem)}

                {/* Load more trigger element for Intersection Observer */}
                {hasNextPage && <div ref={loadMoreRef} className="h-4 w-full" />}

                {isFetchingNextPage && (
                  <div className="flex justify-center py-2">
                    <RiLoader2Line className="animate-spin" size={16} />
                  </div>
                )}
              </>
            )}
          </div>
        </div>
        <div className="flex flex-col mb-2">
          <LabelCustom
            isRequire={true}
            className="mb-2"
            label={t('common.facebookAds.audiences.segment')}
          />
          <Select
            onValueChange={(value) => setPayload((prev) => ({ ...prev, segment_update_id: value }))}
          >
            <SelectTrigger className="w-full h-10 rounded-xl">
              <SelectValue placeholder={t('common.facebookAds.audiences.segmentPlaceholder')} />
            </SelectTrigger>
            <SelectContent className="max-h-[250px] overflow-auto p-2 rounded-xl">
              {!!newOptions.length ? (
                newOptions.map((item) => (
                  <SelectItem
                    className={cn(
                      'text-sm p-2 rounded-md cursor-pointer',
                      item.value === payload.segment_update_id &&
                        '!bg-brand text-white hover:text-white focus:text-white',
                    )}
                    key={item.value}
                    value={item.value}
                  >
                    <p>
                      {item.label}
                      <span className="text-xs text-big360Color-neutral-500">
                        {' - '}
                        {Number(item.count).toLocaleString()} {t('common.contacts')}
                      </span>
                    </p>
                  </SelectItem>
                ))
              ) : (
                <NoData />
              )}
            </SelectContent>
          </Select>
        </div>
        <div className="flex items-end justify-end gap-3">
          <Button
            onClick={() => {
              if (onClose) {
                onClose();
              }
            }}
            className="px-3 py-1 rounded-xl w-full"
            variant={'outline'}
            size={'lg'}
          >
            {t('common.button.cancel')}
          </Button>
          <Button
            onClick={handleSubmit}
            className="px-3 py-1 rounded-xl w-full"
            size={'lg'}
            disabled={mutationUpdate.isPending || !payload.segment_update_id}
          >
            {mutationUpdate.isPending ? (
              <RiLoader2Line className="animate-spin" />
            ) : (
              t('common.update')
            )}
          </Button>
        </div>
      </div>
      <LimitUpdateAudience
        open={openWarning}
        setOpen={(open) => {
          setOpenWarning(open);
          if (!open && onClose) {
            onClose();
          }
        }}
      />
    </>
  );
};
export default UpdateCustomAudience;
