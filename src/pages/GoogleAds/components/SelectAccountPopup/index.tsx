import Modal from '@/components/Modal';
import { LoadingButtonIcon } from '@/components/Loading/LoadingButton';
import { useGoogleContext } from '@/pages/GoogleAds/context/GoogleAuthContext';
import TitlePopup from './TitlePopup';
import { ListBusiness } from '@/pages/GoogleAds/components/ListBusiness';
import { Button } from '@/components/ui/button';
import { useTranslation } from 'react-i18next';
import { Box } from '@/components/Box';
import { IAdsAccount } from '@/types/google';
import { googleOauthApi } from '@/apis/googleOauth';
import { toast } from '@/hooks/use-toast';
import { RESPONSE_MESSAGES } from '@/constants/messages/response.message';
import { useState, useEffect } from 'react';
import { NoticeWarning } from '@/components/Notice/NoticeWarning';

type Props = {
  open: boolean;
  setOpen?: (value: boolean) => void;
};

const Index = ({ open, setOpen }: Props) => {
  const { adsAccount, refetchLoading, handleRefetchListPage, updateStateSelected } =
    useGoogleContext();
  const { t } = useTranslation();
  const [account, setAccount] = useState<IAdsAccount>({
    ad_account_id: adsAccount?.ad_account_id ?? '',
    ad_account_name: adsAccount?.ad_account_name ?? '',
  });

  useEffect(() => {
    if (adsAccount) {
      setAccount({
        ad_account_id: adsAccount.ad_account_id,
        ad_account_name: adsAccount.ad_account_name,
      });
    }
  }, [adsAccount]);

  const onCloseModal = () => setOpen && setOpen(false);

  const handleSelectPage = async () => {
    const { ad_account_id } = account;
    try {
      await googleOauthApi.selectGoogleAdsAccount(ad_account_id);
      toast({
        status: 'success',
        description: RESPONSE_MESSAGES.SELECT_DEFAULT,
      });
      updateStateSelected(ad_account_id);
      handleRefetchListPage();
      onCloseModal();
    } catch (error) {
      return error;
    }
  };
  
  return (
    <Modal
      className="max-w-[684px] gap-3 max-h-svh overflow-auto"
      isCloseIcon={true}
      onOpenChange={setOpen}
      openModal={open}
      trigger={<></>}
    >
      <TitlePopup />

      <NoticeWarning>
        <p>{t('googleAds.selectAdvertiserNotice')}</p>
      </NoticeWarning>

      {refetchLoading ? (
        <div className="w-full border-[1px] border-secondary rounded-2xl h-[264px] flex items-center justify-center">
          <LoadingButtonIcon width={'50px'} height={'50px'} />
        </div>
      ) : (
        <ListBusiness setAccount={setAccount} selectedAccountId={account.ad_account_id} />
      )}

      <Box className="justify-end gap-[10px]">
        <Button onClick={handleRefetchListPage} className="w-[142px]" variant={'outline'}>
          {t('common.button.reload')}
        </Button>
        <Button
          onClick={handleSelectPage}
          disabled={!account.ad_account_id || adsAccount?.ad_account_id === account.ad_account_id}
          className="w-[142px]"
          variant={'primary'}
        >
          {t('common.button.select')}
        </Button>
      </Box>
    </Modal>
  );
};
export default Index;
