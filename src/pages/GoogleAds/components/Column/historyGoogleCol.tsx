import { formatDateTime } from '@/utils/helper';
import { ColumnDef } from '@tanstack/react-table';
import { t } from 'i18next';
import { IGoogleHistoryItem } from '@/types/google';
import { Box } from '@/components/Box';
import { Badge } from '@/components/ui/badge';

const historyGoogleCol = (): ColumnDef<IGoogleHistoryItem>[] => {
  return [
    {
      accessorKey: 'date_time',
      header: () => (
        <div className="text-left w-full">
          {t('common.date')} & {t('common.time')}
        </div>
      ),
      cell: ({ row }) => {
        const date = new Date();
        return (
          <div className="text-left">
            {formatDateTime(row.original.date_completed ?? date, '/', ':')}
          </div>
        );
      },
      size: 130,
    },
    {
      accessorKey: 'segment_added',
      header: () => <div className="text-left w-full">{t('segmentLog.segmentAdded')}</div>,
      cell: ({ row }) => {
        const isInitial = row.original.is_initial;
        return isInitial ? (
          <Box className="justify-start">
            <div className="text-left font-medium truncate">{row.original.segment_info.name}</div>
            <Badge variant={'process'}>Initial</Badge>
          </Box>
        ) : (
          <div className="text-left font-medium truncate">{row.original.segment_info.name}</div>
        );
      },
      size: 250,
    },
    {
      accessorKey: 'contacts',
      header: () => <div className="text-right w-full">{t('common.contacts')}</div>,
      cell: ({ row }) => {
        const status = row.original.status;
        switch (status) {
          case 'COMPLETED':
            return (
              <div className="text-right font-medium text-big360Color-success-500">
                +{Number(row.original.records).toLocaleString()}
              </div>
            );
          case 'FAILED':
            return <div className="text-right font-medium text-big360Color-danger-500">0</div>;
          default:
            return (
              <div className="text-right font-medium">
                {Number(row.original.records).toLocaleString()}
              </div>
            );
        }
      },
      size: 100,
    },
  ];
};

export default historyGoogleCol;
