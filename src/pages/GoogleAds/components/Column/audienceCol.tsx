import { formatDateTime } from '@/utils/helper';
import { ColumnDef } from '@tanstack/react-table';
import { t } from 'i18next';
import { DetailAudience } from '@/components/DetailAudience';
import { IAudienceDetail } from '@/types/audience';
import { TFilterAudience } from '@/types/facebook';

type Props = {
  act: string;
  setIdActive: (id: number) => void;
  onOpenHistoryModal?: (detail: IAudienceDetail) => void;
  onOpenUpdateModal?: (detail: IAudienceDetail) => void;
  filterPayload: TFilterAudience
};

const audienceCol = ({
  act,
  setIdActive,
  onOpenHistoryModal,
  onOpenUpdateModal,
  filterPayload
}: Props): ColumnDef<IAudienceDetail>[] => {
  return [
    {
      accessorKey: 'audience_name',
      header: () => t('common.facebookAds.customAudience'),
      cell: ({ row, ...context }) => {
        return (
          <DetailAudience
            detail={row.original}
            setIdActive={setIdActive}
            act={act}
            type={'GOOGLE'}
            onOpenHistoryModal={onOpenHistoryModal}
            onOpenUpdateModal={onOpenUpdateModal}
            /* eslint-disable @typescript-eslint/no-explicit-any */
            popoverStates={(context as any).popoverStates}
            setPopoverStates={(context as any).setPopoverStates}
            refreshingRows={(context as any).refreshingRows}
            setRefreshingRows={(context as any).setRefreshingRows}
            rowId={(context as any).rowId}
            filterPayload={filterPayload}
          />
        );
      },
      minSize: 150,
      size: 270,
      meta: {
        sticky: 'left',
      },
    },
    {
      accessorKey: 'segment.name',
      header: () => t('segment.title'),
      cell: ({ row }) => {
        return (
          <div className="flex items-center gap-2">
            <span className="line-clamp-1">{row.original.segment_name}</span>
          </div>
        );
      },
      size: 200,
    },
    {
      accessorKey: 'number_contact',
      header: () => <div className="text-right w-full">{t('segment.totalContacts')}</div>,
      cell: ({ row }) => {
        return (
          <div className="text-right w-full">
            {Number(row.original.total_records).toLocaleString()}
          </div>
        );
      },
      size: 150,
    },
    {
      accessorKey: 'date_created',
      header: () => <div className="text-right w-full">{t('common.lastUpdate')}</div>,
      cell: ({ row }) => {
        return (
          <div className="text-right">{formatDateTime(row.original.updated_at, '/', ':')}</div>
        );
      },
      size: 200,
    },
  ];
};

export default audienceCol;
