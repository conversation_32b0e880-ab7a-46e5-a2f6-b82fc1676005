import SearchBar from '@/components/SearchBar';
import { useEffect, useState } from 'react';
import { TFilterAudience } from '@/types/facebook';
import DateRangePicker from '@/components/DateRanger/DateRangerPicker';
import { useTranslation } from 'react-i18next';

type TFilter = Omit<TFilterAudience, 'currentPage' | 'pageSize'>;

type Props = {
  setFilterPayload: (filterPayload: TFilter) => void;
  filterPayload: TFilter;
};

const FilterPanel = ({ filterPayload, setFilterPayload }: Props) => {
  const [search, setSearch] = useState<string>('');
  const [isSearch, setIsSearch] = useState<boolean>(false);
  const { t } = useTranslation();
  useEffect(() => {
    setSearch(filterPayload.search || '');
  }, [filterPayload.search]);

  const datePickerKey = `${filterPayload.date_created_from}-${filterPayload.date_created_to}`;

  useEffect(() => {
    if (!isSearch) {
      return;
    }
    const timeOut = setTimeout(() => {
      setFilterPayload({ ...filterPayload, search });
    }, 300);
    return () => clearTimeout(timeOut);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [search]);

  return (
    <div className="flex w-full gap-3 mb-4 mt-4">
      <SearchBar
        className="flex-1"
        placeholder={t('audience.searchCustomAudience')}
        value={search ?? ''}
        setSearchQuery={(value) => {
          setSearch(value);
          setIsSearch(true);
        }}
      />
      <DateRangePicker
        key={datePickerKey}
        placeholder={t('googleAds.pickADate')}
        initialDateFrom={filterPayload.date_created_from || ''}
        initialDateTo={filterPayload.date_created_to || ''}
        onChange={(date) => {
          if (!date.from || !date.to) {
            return;
          }
          setFilterPayload({
            ...filterPayload,
            date_created_from: new Date(date.from || '').toISOString(),
            date_created_to: new Date(date.to || '').toISOString(),
          });
        }}
        onClear={() => {
          setFilterPayload({
            ...filterPayload,
            date_created_from: '',
            date_created_to: '',
          });
        }}
      />
    </div>
  );
};
export default FilterPanel;
