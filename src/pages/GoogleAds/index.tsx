import EmptyGoogleDataView from './views/EmptyGoogleDataView';
import Breadcrumb from '@/components/Breadcrumb';
import HeaderGoogle from '@/pages/GoogleAds/components/HeaderGoogle';
import { useGoogleContext } from '@/pages/GoogleAds/context/GoogleAuthContext';
import { useState } from 'react';
import Modal from '@/components/Modal';
import HistoryGoogle from '@/pages/GoogleAds/components/HistoryGoogle';
import UpdateCustomAudience from '@/pages/GoogleAds/components/UpdateCustomAudience';
import { IAudienceDetail } from '@/types/audience';
import { useTranslation } from 'react-i18next';
import { useQueryClient } from '@tanstack/react-query';
import { QUERY_KEY } from '@/utils/constants';
import ContainerGoogleAds from './components/ContainerGoogleAds';

const GoogleAdsPage = () => {
  const { isAccountSelected } = useGoogleContext();
  const { t } = useTranslation();
  const queryClient = useQueryClient();

  // Modal states
  const [historyModal, setHistoryModal] = useState<{
    isOpen: boolean;
    audienceDetail: IAudienceDetail | null;
  }>({
    isOpen: false,
    audienceDetail: null,
  });

  const [updateModal, setUpdateModal] = useState<{
    isOpen: boolean;
    audienceDetail: IAudienceDetail | null;
  }>({
    isOpen: false,
    audienceDetail: null,
  });

  const handleUpdateAudienceDetail = () => {
    queryClient.invalidateQueries({
      queryKey: [QUERY_KEY.GOOGLE_AUDIENCE],
    });
  };

  const openHistoryModal = (detail: IAudienceDetail) => {
    setHistoryModal({
      isOpen: true,
      audienceDetail: detail,
    });
  };

  const closeHistoryModal = () => {
    setHistoryModal({
      isOpen: false,
      audienceDetail: null,
    });
  };

  const openUpdateModal = (detail: IAudienceDetail) => {
    setUpdateModal({
      isOpen: true,
      audienceDetail: detail,
    });
  };

  const closeUpdateModal = () => {
    setUpdateModal({
      isOpen: false,
      audienceDetail: null,
    });
  };

  return (
    <div className="w-full h-full flex flex-col">
      <Breadcrumb />

      {!isAccountSelected ? (
        <>
          <HeaderGoogle />
          <EmptyGoogleDataView />
        </>
      ) : (
        <ContainerGoogleAds
          onOpenHistoryModal={openHistoryModal}
          onOpenUpdateModal={openUpdateModal}
        />
      )}

      {/* History Modal */}
      <Modal
        openModal={historyModal.isOpen}
        onOpenChange={(open) => {
          if (!open) closeHistoryModal();
        }}
        className="max-w-[920px] w-full h-[592px]"
        title={
          <div className="h-[40px]">
            <p className="text-lg font-semibold text-big360Color-neutral-950">
              {t('common.updateHistory')}
            </p>
            <p className="text-sm font-normal text-big360Color-neutral-700">
              {historyModal.audienceDetail?.audience_name || t('common.customAudienceName')}
            </p>
          </div>
        }
      >
        {historyModal.audienceDetail && (
          <HistoryGoogle
            jobId={historyModal.audienceDetail.job_id}
            totalContact={historyModal.audienceDetail.total_records}
            onUpdateAudienceDetail={handleUpdateAudienceDetail}
          />
        )}
      </Modal>

      {/* Update Modal */}
      <Modal
        openModal={updateModal.isOpen}
        onOpenChange={(open) => {
          if (!open) closeUpdateModal();
        }}
        titleAlign={'center'}
        className="max-w-[650px] w-full h-[534px]"
        title={
          <div className="h-[40px]">
            <p className="text-lg font-semibold text-big360Color-neutral-950">
              {t('common.update')} {t('common.facebookAds.customAudience')}
            </p>
            <p className="text-sm font-normal text-big360Color-neutral-700">
              {updateModal.audienceDetail?.audience_name || ''}
            </p>
          </div>
        }
      >
        {updateModal.audienceDetail && (
          <UpdateCustomAudience detail={updateModal.audienceDetail} onClose={closeUpdateModal} />
        )}
      </Modal>
    </div>
  );
};

export default GoogleAdsPage;
