import CustomToolTips from '@/components/CustomToolTips';
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Textarea } from '@/components/ui/textarea';
import { cn } from '@/lib/utils';
import { Control, FieldPath, FieldValues } from 'react-hook-form';
import { RiInformation2Line } from '@remixicon/react';

type Props<T extends FieldValues> = {
  control: Control<T>;
  name: FieldPath<T>;
  placeholder?: string;
  title?: string;
  isRequired?: boolean;
  tooltipContent?: string;
};

const TextareaField = <T extends FieldValues>({
  title,
  control,
  name,
  placeholder,
  isRequired = true,
  tooltipContent = '',
}: Props<T>) => {
  return (
    <FormField
      control={control}
      name={name}
      render={({ field, formState }) => (
        <FormItem className="flex flex-col items-start gap-2 w-full">
          <FormLabel className="flex items-center">
            {title} {isRequired && <span className="text-red-500">*</span>}
            {tooltipContent && (
              <CustomToolTips
                className="bg-card border w-[346px] text-primary text-xs"
                content={<div className="bg-card">{tooltipContent}</div>}
                element={<RiInformation2Line className="cursor-pointer ml-2" size={16} />}
              />
            )}
          </FormLabel>
          <FormControl>
            <Textarea
              className={cn(
                'text-sm p-3 rounded-xl h-[131px] resize-none',
                formState.errors[name]
                  ? 'border-red-500'
                  : 'focus:shadow-medium focus:border-primary-crm',
              )}
              placeholder={placeholder}
              {...field}
              value={typeof field.value === 'string' ? field.value : ''}
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
};
export default TextareaField;
