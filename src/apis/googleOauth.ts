import { APIConfig } from '@/apis/index';
import { ENDPOINTS } from '@/apis/endpoints';

const getGoogleUser = async ({ signal }: { signal: AbortSignal }) => {
  const res = await APIConfig().get(ENDPOINTS.google[''], {
    signal,
  });
  return res.data;
};

const selectGoogleAdsAccount = async (ad_account_id: string) => {
  const res = await APIConfig().post(
    ENDPOINTS.google.ad_account,
    {},
    {
      withCredentials: true,
      params: { ad_account_id },
    },
  );
  return res.data;
};

const getGoogleOauthUrl = async (redirect_path: string) => {
  const res = await APIConfig().get(ENDPOINTS.google.authLink, {
    withCredentials: true,
    params: { redirect_path },
  });
  return res.data;
};

const disconnect = async () => {
  const res = await APIConfig().post(ENDPOINTS.google.logout);
  return res.data;
};

const uploadAudience = async (payload: { audience_name: string; segment_id: string }) => {
  const res = await APIConfig().post(ENDPOINTS.google.largeUpload, payload);
  return res.data;
};

const updateAudience = async (payload: { job_id: number; segment_update_id: string }) => {
  const res = await APIConfig().post(ENDPOINTS.google.largeUpdate, payload);
  return res.data;
};

export const googleOauthApi = {
  getGoogleOauthUrl,
  selectGoogleAdsAccount,
  getGoogleUser,
  disconnect,
  uploadAudience,
  updateAudience
};
