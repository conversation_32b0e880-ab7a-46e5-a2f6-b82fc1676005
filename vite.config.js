import { defineConfig, loadEnv } from 'vite';
import react from '@vitejs/plugin-react';
import path, { dirname } from 'path';
import federation from '@originjs/vite-plugin-federation';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// https://vite.dev/config/
export default defineConfig(async ({ mode }) => {
  process.env = { ...process.env, ...loadEnv(mode, process.cwd()) };

  const BIG360_URL = process.env.VITE_BIG360_URL;
  const getBIG360RemoteUrl = () => {
    const timestamp = Date.now();
    return `${BIG360_URL}/assets/remoteEntry.js?v=${timestamp}`;
  };

  return {
    plugins: [
      react(),
      federation({
        name: 'crm360',
        remotes: {
          big360: getBIG360RemoteUrl(),
        },
        filename: 'remoteEntry.js',
        exposes: {
          './App': './src/App',
        },
        shared: [
          'react',
          'react-dom',
          'react-router-dom',
          '@tanstack/react-query',
          'i18next',
          'react-redux',
          '@reduxjs/toolkit',
        ],
      }),
    ],
    build: {
      modulePreload: false,
      target: 'esnext',
      minify: false,
      cssCodeSplit: false,
    },
    // define: {
    //   REACT_APP_: JSON.stringify(process.env.REACT_APP_),
    // },
    resolve: {
      alias: {
        '@/store': path.resolve(__dirname, 'src/store'),
        '@/components': path.resolve(__dirname, 'src/components'),
        '@/apis': path.resolve(__dirname, 'src/apis'),
        '@/lib': path.resolve(__dirname, 'src/lib'),
        '@/pages': path.resolve(__dirname, 'src/pages'),
        '@/assets': path.resolve(__dirname, 'src/assets'),
        '@/constants': path.resolve(__dirname, 'src/constants'),
        '@/ui': path.resolve(__dirname, 'src/components/ui'),
        '@/types': path.resolve(__dirname, 'src/types'),
        '@/utils': path.resolve(__dirname, 'src/utils'),
        '@/hooks': path.resolve(__dirname, 'src/hooks'),
        '@/locale': path.resolve(__dirname, 'src/locale'),
        '@/config-translation': path.resolve(__dirname, 'src/config-translation'),
      },
    },
    envPrefix: 'REACT_APP_',
    server: {
      cors: true,
    },
  };
});
